package act

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"go-hetu/components"
	"go-hetu/services/act"
	"go-hetu/services/act/data"
)

type resource struct {
	service act.Service
}

func RegisterHandlers(
	actRouter *gin.RouterGroup,
	actService act.Service,
) {
	res := resource{
		service: actService,
	}

	toggleRouter := actRouter.Group("/toggle/config")
	{
		toggleRouter.POST("/instlist", res.InstList)
		toggleRouter.POST("/batchaddinst", res.BatchAddInst)
		toggleRouter.POST("/batchupdateinst", res.BatchUpdateInst)
		toggleRouter.POST("/actlist", res.ActList)
		toggleRouter.POST("/updateact", res.UpdateAct)
		toggleRouter.POST("/batchupdateact", res.BatchUpdateAct)
		toggleRouter.POST("/batchaddact", res.BatchAddAct)
		toggleRouter.POST("/operatorlist", res.OperatorList)
	}

	toolsRouter := actRouter.Group("/tools")
	{
		toolsRouter.POST("/flushHistoryInstActConfig", res.FlushHistoryInstActConfig) // 刷历史的直接关停活动配置数据
	}
}

func (r resource) FlushHistoryInstActConfig(ctx *gin.Context) {
	var params data.FlushHistoryInstActConfigReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	err := r.service.FlushHistoryInstActConfig(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) InstList(ctx *gin.Context) {
	var params data.ListInstReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if params.PageNo == 0 || params.PageSize == 0 {
		params.PageNo = 1
		params.PageSize = 20
	}
	if params.StartTime > params.EndTime {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	resp, err := r.service.InstList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, resp)
}

func (r resource) BatchAddInst(ctx *gin.Context) {
	var params data.BatchAddInstReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := r.service.BatchAddInst(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) BatchUpdateInst(ctx *gin.Context) {
	var params data.BatchUpdateInstReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := r.service.BatchUpdateInst(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) ActList(ctx *gin.Context) {
	var params data.ActListReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if params.PageNo == 0 || params.PageSize == 0 {
		params.PageNo = 1
		params.PageSize = 20
	}
	if params.StartTime > params.EndTime {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	resp, err := r.service.ActList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, resp)
}

func (r resource) UpdateAct(ctx *gin.Context) {
	var params data.UpdateActReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := r.service.UpdateAct(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) BatchUpdateAct(ctx *gin.Context) {
	var params data.BatchUpdateActReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := r.service.BatchUpdateAct(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) BatchAddAct(ctx *gin.Context) {
	var params data.BatchAddActReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	err := r.service.BatchAddAct(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) OperatorList(ctx *gin.Context) {
	var params data.OperatorListReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if params.PageNo == 0 || params.PageSize == 0 {
		params.PageNo = 1
		params.PageSize = 20
	}
	resp, err := r.service.OperatorList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, resp)
}
