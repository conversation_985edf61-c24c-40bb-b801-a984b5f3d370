package mq

import (
	"encoding/json"
	"fmt"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go-hetu/data"
	"go-hetu/helpers"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

func Tag900001Consumer(ctx *gin.Context, rmqMsg rmq.Message) (err error) {
	startTime := time.Now()
	var msg = data.AddInstShutConfig{}
	if err = json.Unmarshal(rmqMsg.GetContent(), &msg); err != nil {
		zlog.Warnf(ctx, "json unmarshal error: %s", err)
		return err
	}
	zlog.Infof(ctx, "Tag900001Consumer: %+v", msg)

	// 处理每个机构ID
	for _, instId := range msg.InstIDs {
		if err = processInstActShut(ctx, instId, msg.OperatorType); err != nil {
			zlog.Errorf(ctx, "processInstActShut failed for instId=%d, err=%v", instId, err)
			// 继续处理其他机构，不因为一个失败而中断
			continue
		}
	}

	// 记录处理结果和性能指标
	duration := time.Since(startTime)
	zlog.Infof(ctx, "Tag900001Consumer completed: total=%d, duration=%v", len(msg.InstIDs), duration)
	return nil
}

func processInstActShut(ctx *gin.Context, instId int64, operatorType int) (err error) {
	zlog.Infof(ctx, "Processing inst act shut for instId: %d, operatorType: %d", instId, operatorType)

	// 使用并发查询提高性能
	type result struct {
		actIds []int64
		err    error
		name   string
	}

	resultChan := make(chan result, 2)

	// 并发查询白名单机构活动
	go func() {
		actIds, err := getWhiteListActivities(ctx, instId)
		resultChan <- result{actIds: actIds, err: err, name: "whitelist"}
	}()

	// 并发查询所有机构活动
	go func() {
		actIds, err := getAllInstActivities(ctx, instId)
		resultChan <- result{actIds: actIds, err: err, name: "all-inst"}
	}()

	// 收集结果
	var allActIds []int64
	for i := 0; i < 2; i++ {
		res := <-resultChan
		if res.err != nil {
			zlog.Errorf(ctx, "%s activities query failed for instId=%d, err=%v", res.name, instId, res.err)
			return res.err
		}
		allActIds = append(allActIds, res.actIds...)
		zlog.Infof(ctx, "Found %d %s activities for instId=%d", len(res.actIds), res.name, instId)
	}

	// 去重
	allActIds = removeDuplicateInt64(allActIds)

	// 3、查到所有的活动之后，写入表tblAFXInstActToggleConfig
	if len(allActIds) > 0 {
		if err = insertInstActToggleConfig(ctx, instId, allActIds, operatorType); err != nil {
			zlog.Errorf(ctx, "insertInstActToggleConfig failed for instId=%d, err=%v", instId, err)
			return err
		}
		zlog.Infof(ctx, "Successfully processed %d activities for instId=%d with operatorType=%d", len(allActIds), instId, operatorType)
	} else {
		zlog.Infof(ctx, "No activities found for instId=%d", instId)
	}

	return nil
}

// getWhiteListActivities 查询配置该机构为白名单的所有活动
func getWhiteListActivities(ctx *gin.Context, instId int64) (actIds []int64, err error) {
	instIdStr := strconv.FormatInt(instId, 10)

	// 使用JSON查询优化性能，避免在应用层解析所有JSON
	if err = helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAfxAct).
		Select("act_id").
		Where("rule_type = ? AND deleted = 0 AND JSON_CONTAINS(ext_data, ?, '$.whiteList')", 1, fmt.Sprintf(`"%s"`, instIdStr)).
		Pluck("act_id", &actIds).Error; err != nil {
		// 如果JSON查询失败，回退到原来的方法
		zlog.Warnf(ctx, "JSON query failed, fallback to manual parsing: %v", err)
		return getWhiteListActivitiesFallback(ctx, instId)
	}

	return actIds, nil
}

// getWhiteListActivitiesFallback 回退方法，手动解析JSON
func getWhiteListActivitiesFallback(ctx *gin.Context, instId int64) (actIds []int64, err error) {
	type ActResult struct {
		ActId   int64  `gorm:"column:act_id"`
		ExtData string `gorm:"column:ext_data"`
	}

	var acts []ActResult
	if err = helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAfxAct).
		Select("act_id, ext_data").
		Where("rule_type = ? AND deleted = 0", 1).
		Find(&acts).Error; err != nil {
		return nil, fmt.Errorf("query whitelist activities failed: %w", err)
	}

	instIdStr := strconv.FormatInt(instId, 10)
	for _, act := range acts {
		// 解析ext_data中的whiteList
		if isInstInWhiteList(act.ExtData, instIdStr) {
			actIds = append(actIds, act.ActId)
		}
	}

	return actIds, nil
}

// isInstInWhiteList 检查机构ID是否在白名单中
func isInstInWhiteList(extData, instId string) bool {
	if extData == "" {
		return false
	}

	// 解析JSON格式的ext_data
	var extDataMap map[string]interface{}
	if err := json.Unmarshal([]byte(extData), &extDataMap); err != nil {
		return false
	}

	// 获取whiteList
	whiteListInterface, exists := extDataMap["whiteList"]
	if !exists {
		return false
	}

	// 转换为字符串数组
	whiteListArray, ok := whiteListInterface.([]interface{})
	if !ok {
		return false
	}

	// 检查instId是否在白名单中
	for _, item := range whiteListArray {
		if itemStr, ok := item.(string); ok && itemStr == instId {
			return true
		}
	}

	return false
}

// getAllInstActivities 查询配置了所有机构的活动
func getAllInstActivities(ctx *gin.Context, instId int64) (actIds []int64, err error) {
	var registers []tfmodels.QudaoInstRegister
	if err = helpers.MysqlClientQD.WithContext(ctx).Table(tfmodels.TblQudaoInstRegister).
		Select("promote_business, promote_grade_dept, promote_lv1_new, tag_info").
		Where("run_status in (1, 2) AND inst_id = ? AND deleted = 0", instId).
		Find(&registers).Error; err != nil {
		return nil, fmt.Errorf("query register records failed: %w", err)
	}

	if len(registers) == 0 {
		return []int64{}, nil
	}

	// 2.2 根据查到的信息去查询tblAFXAct活动表，rule_type=0的
	for _, register := range registers {
		// 解析tag_info获取tag_ids
		tagIds := parseTagInfo(register.TagInfo)

		// 查询匹配的活动
		matchedActIds, err := findMatchingActivities(ctx, register.PromoteBusiness, register.PromoteGradeDept, register.PromoteLv1New, tagIds)
		if err != nil {
			zlog.Errorf(ctx, "findMatchingActivities failed: %v", err)
			continue
		}
		actIds = append(actIds, matchedActIds...)
	}

	// 去重
	actIds = removeDuplicateInt64(actIds)
	return actIds, nil
}

// parseTagInfo 解析tag_info字段，提取tag_id
func parseTagInfo(tagInfo string) []int {
	if tagInfo == "" {
		return []int{}
	}

	var tagArray []map[string]interface{}
	if err := json.Unmarshal([]byte(tagInfo), &tagArray); err != nil {
		return []int{}
	}

	var tagIds []int
	for _, tag := range tagArray {
		if keyInterface, exists := tag["key"]; exists {
			if keyFloat, ok := keyInterface.(int); ok {
				tagIds = append(tagIds, keyFloat)
			}
		}
	}

	return tagIds
}

// findMatchingActivities 查找匹配的活动
func findMatchingActivities(ctx *gin.Context, promoteBusiness, promoteGradeDept, promoteLv1New int, tagIds []int) (actIds []int64, err error) {
	if len(tagIds) == 0 {
		return []int64{}, nil
	}

	// 构建SQL查询条件，使用数据库层面的字符串匹配
	var conditions []string
	var args []interface{}

	for _, tagId := range tagIds {
		// 构建LIKE查询条件，匹配 ",tagId," 格式
		conditions = append(conditions, "channel_labels LIKE ?")
		args = append(args, fmt.Sprintf("%%,%d,%%", tagId))
	}

	whereClause := fmt.Sprintf("rule_type = ? AND deleted = 0 AND (%s)", strings.Join(conditions, " OR "))
	queryArgs := append([]interface{}{0}, args...)

	if err = helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAfxAct).
		Select("act_id").
		Where(whereClause, queryArgs...).
		Pluck("act_id", &actIds).Error; err != nil {
		return nil, fmt.Errorf("query all-inst activities failed: %w", err)
	}

	return actIds, nil
}

// isActivityMatching 检查活动的channel_labels是否与tag_ids匹配
func isActivityMatching(channelLabels string, tagIds []int) bool {
	if channelLabels == "" || len(tagIds) == 0 {
		return false
	}

	// channel_labels格式为 ",3,2,1,"
	// 去掉首尾的逗号，然后分割
	channelLabels = strings.Trim(channelLabels, ",")
	if channelLabels == "" {
		return false
	}

	channelTagStrs := strings.Split(channelLabels, ",")
	channelTagIds := make(map[int]bool)
	for _, tagStr := range channelTagStrs {
		if tagStr != "" {
			if tagId, err := strconv.Atoi(tagStr); err == nil {
				channelTagIds[tagId] = true
			}
		}
	}

	// 检查是否有交集
	for _, tagId := range tagIds {
		if channelTagIds[tagId] {
			return true
		}
	}

	return false
}

// insertInstActToggleConfig 将活动配置写入或更新tblAFXInstActToggleConfig表
func insertInstActToggleConfig(ctx *gin.Context, instId int64, actIds []int64, operatorType int) (err error) {
	if len(actIds) == 0 {
		return nil
	}

	// 根据operatorType设置状态：1-开启，2-关闭
	var statusStr string
	switch operatorType {
	case 1:
		statusStr = "开启"
	case 2:
		statusStr = "关闭"
	default:
		statusStr = "未知"
	}

	// 使用事务确保数据一致性
	return helpers.ExecWithTransaction(ctx, helpers.MysqlClient, func(tx *gorm.DB) error {
		// 检查已存在的记录
		var existingActIds []int64
		if err := tx.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
			Select("act_id").
			Where("inst_id = ? AND act_id IN ? AND deleted = 0", instId, actIds).
			Pluck("act_id", &existingActIds).Error; err != nil {
			return fmt.Errorf("check existing records failed: %w", err)
		}

		// 分类已存在和不存在的记录
		existingMap := make(map[int64]bool)
		for _, actId := range existingActIds {
			existingMap[actId] = true
		}

		var newActIds []int64
		var updateActIds []int64
		for _, actId := range actIds {
			if existingMap[actId] {
				updateActIds = append(updateActIds, actId)
			} else {
				newActIds = append(newActIds, actId)
			}
		}

		now := time.Now().Unix()
		var insertCount, updateCount int

		// 1. 更新已存在的记录
		if len(updateActIds) > 0 {
			updateData := map[string]interface{}{
				"status":      operatorType,
				"operator":    "system",
				"update_time": now,
			}
			result := tx.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
				Where("inst_id = ? AND act_id IN ? AND deleted = 0", instId, updateActIds).
				Updates(updateData)
			if result.Error != nil {
				return fmt.Errorf("batch update inst act toggle config failed: %w", result.Error)
			}
			updateCount = int(result.RowsAffected)
			zlog.Infof(ctx, "Updated %d existing activity configs for instId=%d with status=%s(%d)",
				updateCount, instId, statusStr, operatorType)
		}

		// 2. 插入新记录
		if len(newActIds) > 0 {
			var configs []tfmodels.AFXInstActToggleConfig
			for _, actId := range newActIds {
				config := tfmodels.AFXInstActToggleConfig{
					InstID:     instId,
					ActID:      actId,
					Status:     operatorType, // 使用operatorType作为状态值
					Operator:   "system",
					CreateTime: now,
					UpdateTime: now,
					Deleted:    0,
				}
				configs = append(configs, config)
			}

			// 批量插入，使用更大的批次大小提高性能
			if err := tx.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
				CreateInBatches(configs, 500).Error; err != nil {
				return fmt.Errorf("batch insert inst act toggle config failed: %w", err)
			}
			insertCount = len(configs)
			zlog.Infof(ctx, "Inserted %d new activity configs for instId=%d with status=%s(%d)",
				insertCount, instId, statusStr, operatorType)
		}

		if insertCount == 0 && updateCount == 0 {
			zlog.Infof(ctx, "No activities to process for instId=%d", instId)
		} else {
			zlog.Infof(ctx, "Successfully processed %d activities for instId=%d: inserted=%d, updated=%d, status=%s(%d)",
				len(actIds), instId, insertCount, updateCount, statusStr, operatorType)
		}

		return nil
	})
}

// removeDuplicateInt64 去重int64数组，优化版本
func removeDuplicateInt64(slice []int64) []int64 {
	if len(slice) <= 1 {
		return slice
	}

	// 使用map去重，预分配容量提高性能
	seen := make(map[int64]struct{}, len(slice))
	result := make([]int64, 0, len(slice))

	for _, item := range slice {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}

	return result
}
