package act

import (
	"testing"

	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"github.com/gin-gonic/gin"
	"go-hetu/services/act/data"
)

// mockRepository 简单的 mock repository 实现
type mockRepository struct {
	// 用于存储测试数据
	instConfigs  []tfmodels.AFXInstToggleConfig
	actConfigs   []tfmodels.AFXInstActToggleConfig
	operatorLogs []tfmodels.AFXInstActToggleOperationLog
	shouldError  bool
}

func newMockRepository() *mockRepository {
	return &mockRepository{
		instConfigs:  make([]tfmodels.AFXInstToggleConfig, 0),
		actConfigs:   make([]tfmodels.AFXInstActToggleConfig, 0),
		operatorLogs: make([]tfmodels.AFXInstActToggleOperationLog, 0),
	}
}

func (m *mockRepository) GetDB(ctx *gin.Context) *dbcontext.DB {
	// 返回 nil，这些测试不使用数据库事务
	return nil
}

func (m *mockRepository) GetInstList(ctx *gin.Context, params data.ListInstReq) (resp data.ListInstResp, err error) {
	if m.shouldError {
		return resp, &testError{msg: "mock error"}
	}

	resp.Total = int64(len(m.instConfigs))
	resp.List = m.instConfigs
	return resp, nil
}

func (m *mockRepository) BatchInsertInstConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstToggleConfig) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	m.instConfigs = append(m.instConfigs, instConfigs...)
	return nil
}

func (m *mockRepository) BatchInsertInstActConfig(ctx *gin.Context, instActConfigs []tfmodels.AFXInstActToggleConfig) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	m.actConfigs = append(m.actConfigs, instActConfigs...)
	return nil
}

func (m *mockRepository) UpdateInstByInstIds(ctx *gin.Context, ids []int64, data map[string]interface{}) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	// 简单模拟更新操作
	return nil
}

func (m *mockRepository) GetActListByIds(ctx *gin.Context, ids []int64) (list []tfmodels.AFXInstActToggleConfig, err error) {
	if m.shouldError {
		return nil, &testError{msg: "mock error"}
	}

	for _, config := range m.actConfigs {
		for _, id := range ids {
			if config.ID == id {
				list = append(list, config)
				break
			}
		}
	}
	return list, nil
}

func (m *mockRepository) GetActList(ctx *gin.Context, params data.ActListReq) (resp data.ActListResp, err error) {
	if m.shouldError {
		return resp, &testError{msg: "mock error"}
	}

	resp.Total = int64(len(m.actConfigs))
	resp.List = m.actConfigs
	return resp, nil
}

func (m *mockRepository) UpdateActByInstActId(ctx *gin.Context, instID int64, actID int64, data map[string]interface{}) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	return nil
}

func (m *mockRepository) BatchAddAct(ctx *gin.Context, insertData []tfmodels.AFXInstActToggleConfig) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	m.actConfigs = append(m.actConfigs, insertData...)
	return nil
}

func (m *mockRepository) BatchInsertOperatorLog(ctx *gin.Context, logData []tfmodels.AFXInstActToggleOperationLog) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	m.operatorLogs = append(m.operatorLogs, logData...)
	return nil
}

func (m *mockRepository) GetOperatorList(ctx *gin.Context, params data.OperatorListReq) (resp data.OperatorListResp, err error) {
	if m.shouldError {
		return resp, &testError{msg: "mock error"}
	}

	resp.Total = int64(len(m.operatorLogs))
	resp.List = m.operatorLogs
	return resp, nil
}

func (m *mockRepository) UpdateActByInstActIds(ctx *gin.Context, ids []int64, data map[string]interface{}) (err error) {
	if m.shouldError {
		return &testError{msg: "mock error"}
	}
	return nil
}



func (m *mockRepository) BatchGetInstActConfigByInstIds(ctx *gin.Context, actID int64, instIds []int) (list []tfmodels.AFXInstActToggleConfig, err error) {
	if m.shouldError {
		return nil, &testError{msg: "mock error"}
	}

	for _, config := range m.actConfigs {
		if config.ActID == actID {
			for _, instId := range instIds {
				if config.InstID == int64(instId) {
					list = append(list, config)
					break
				}
			}
		}
	}
	return list, nil
}

// testError 简单的错误类型
type testError struct {
	msg string
}

func (e *testError) Error() string {
	return e.msg
}

// 创建测试用的 gin.Context
func createTestContext() *gin.Context {
	gin.SetMode(gin.TestMode)
	ctx, _ := gin.CreateTestContext(nil)
	return ctx
}

func TestService_InstList(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.ListInstReq
		wantErr     bool
	}{
		{
			name:        "正常查询",
			shouldError: false,
			params: data.ListInstReq{
				AppID:    1,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name:        "查询失败",
			shouldError: true,
			params: data.ListInstReq{
				AppID:    1,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := newMockRepository()
			mockRepo.shouldError = tt.shouldError

			// 添加测试数据
			if !tt.shouldError {
				mockRepo.instConfigs = append(mockRepo.instConfigs, tfmodels.AFXInstToggleConfig{
					ID:     1,
					AppID:  1,
					InstID: 100,
					Status: 1,
				})
			}

			s := NewService(mockRepo)
			ctx := createTestContext()

			resp, err := s.InstList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("InstList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && resp.Total == 0 {
				t.Errorf("InstList() expected data but got empty result")
			}
		})
	}
}

// BatchAddInst 需要数据库事务，跳过测试
func TestService_BatchAddInst(t *testing.T) {
	t.Skip("跳过 BatchAddInst 测试，因为需要数据库事务")
}

// BatchUpdateInst 需要数据库事务，跳过测试
func TestService_BatchUpdateInst(t *testing.T) {
	t.Skip("跳过 BatchUpdateInst 测试，因为需要数据库事务")
}

func TestService_ActList(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.ActListReq
		wantErr     bool
	}{
		{
			name:        "正常查询",
			shouldError: false,
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name:        "查询失败",
			shouldError: true,
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := newMockRepository()
			mockRepo.shouldError = tt.shouldError

			// 添加测试数据
			if !tt.shouldError {
				mockRepo.actConfigs = append(mockRepo.actConfigs, tfmodels.AFXInstActToggleConfig{
					ID:     1,
					InstID: 100,
					ActID:  200,
					Status: 1,
				})
			}

			s := NewService(mockRepo)
			ctx := createTestContext()

			resp, err := s.ActList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("ActList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && resp.Total == 0 {
				t.Errorf("ActList() expected data but got empty result")
			}
		})
	}
}

func TestService_UpdateAct(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.UpdateActReq
		wantErr     bool
	}{
		{
			name:        "正常更新",
			shouldError: false,
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 2,
			},
			wantErr: false,
		},
		{
			name:        "更新失败",
			shouldError: true,
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 2,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := newMockRepository()
			mockRepo.shouldError = tt.shouldError

			s := NewService(mockRepo)
			ctx := createTestContext()

			err := s.UpdateAct(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateAct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// BatchAddAct 需要数据库事务，跳过测试
func TestService_BatchAddAct(t *testing.T) {
	t.Skip("跳过 BatchAddAct 测试，因为需要数据库事务")
}

func TestService_OperatorList(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.OperatorListReq
		wantErr     bool
	}{
		{
			name:        "正常查询",
			shouldError: false,
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name:        "查询失败",
			shouldError: true,
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := newMockRepository()
			mockRepo.shouldError = tt.shouldError

			// 添加测试数据
			if !tt.shouldError {
				mockRepo.operatorLogs = append(mockRepo.operatorLogs, tfmodels.AFXInstActToggleOperationLog{
					ID:           1,
					InstID:       100,
					ActID:        200,
					Operator:     "test",
					OperatorType: 1,
				})
			}

			s := NewService(mockRepo)
			ctx := createTestContext()

			resp, err := s.OperatorList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("OperatorList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && resp.Total == 0 {
				t.Errorf("OperatorList() expected data but got empty result")
			}
		})
	}
}

// BatchUpdateAct 需要数据库事务，跳过测试
func TestService_BatchUpdateAct(t *testing.T) {
	t.Skip("跳过 BatchUpdateAct 测试，因为需要数据库事务")
}

// FlushHistoryInstActConfig 需要数据库连接，跳过测试
func TestService_FlushHistoryInstActConfig(t *testing.T) {
	t.Skip("跳过 FlushHistoryInstActConfig 测试，因为需要数据库连接")
}
