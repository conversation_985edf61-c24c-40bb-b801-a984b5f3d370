package act

import (
	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"go-hetu/helpers"
	"testing"

	"github.com/gin-gonic/gin"
	"go-hetu/services/act/data"
)

var (
	ctx           = gin.CreateNewContext(gin.New())
	actRepository = NewRepository(dbcontext.New(helpers.MysqlClient))
	actService    = NewService(actRepository)
)

func TestService_InstList(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.ListInstReq
		wantErr     bool
	}{
		{
			name:        "正常查询",
			shouldError: false,
			params: data.ListInstReq{
				AppID:    1,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := actService.InstList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("InstList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && resp.Total == 0 {
				t.Errorf("InstList() expected data but got empty result")
			}
		})
	}
}

// BatchAddInst 需要数据库事务，跳过测试
func TestService_BatchAddInst(t *testing.T) {
	t.Skip("跳过 BatchAddInst 测试，因为需要数据库事务")
}

// BatchUpdateInst 需要数据库事务，跳过测试
func TestService_BatchUpdateInst(t *testing.T) {
	t.Skip("跳过 BatchUpdateInst 测试，因为需要数据库事务")
}

func TestService_ActList(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.ActListReq
		wantErr     bool
	}{
		{
			name:        "正常查询",
			shouldError: false,
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name:        "查询失败",
			shouldError: true,
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			resp, err := actService.ActList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("ActList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && resp.Total == 0 {
				t.Errorf("ActList() expected data but got empty result")
			}
		})
	}
}

func TestService_UpdateAct(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.UpdateActReq
		wantErr     bool
	}{
		{
			name:        "正常更新",
			shouldError: false,
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 2,
			},
			wantErr: false,
		},
		{
			name:        "更新失败",
			shouldError: true,
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 2,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.UpdateAct(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateAct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// BatchAddAct 需要数据库事务，跳过测试
func TestService_BatchAddAct(t *testing.T) {
	t.Skip("跳过 BatchAddAct 测试，因为需要数据库事务")
}

func TestService_OperatorList(t *testing.T) {
	tests := []struct {
		name        string
		shouldError bool
		params      data.OperatorListReq
		wantErr     bool
	}{
		{
			name:        "正常查询",
			shouldError: false,
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name:        "查询失败",
			shouldError: true,
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := actService.OperatorList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("OperatorList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr && resp.Total == 0 {
				t.Errorf("OperatorList() expected data but got empty result")
			}
		})
	}
}

// BatchUpdateAct 需要数据库事务，跳过测试
func TestService_BatchUpdateAct(t *testing.T) {
	t.Skip("跳过 BatchUpdateAct 测试，因为需要数据库事务")
}

// FlushHistoryInstActConfig 需要数据库连接，跳过测试
func TestService_FlushHistoryInstActConfig(t *testing.T) {
	t.Skip("跳过 FlushHistoryInstActConfig 测试，因为需要数据库连接")
}
